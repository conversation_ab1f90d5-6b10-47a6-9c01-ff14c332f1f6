<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple Computer//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
    <dict>
        <key>frames</key>
        <dict>
            <key>b1.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{6,0}</string>
                <key>spriteSize</key>
                <string>{124,124}</string>
                <key>spriteSourceSize</key>
                <string>{142,142}</string>
                <key>textureRect</key>
                <string>{{1,383},{124,124}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>b10.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{6,0}</string>
                <key>spriteSize</key>
                <string>{104,118}</string>
                <key>spriteSourceSize</key>
                <string>{142,142}</string>
                <key>textureRect</key>
                <string>{{139,233},{104,118}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>b11.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{6,0}</string>
                <key>spriteSize</key>
                <string>{102,116}</string>
                <key>spriteSourceSize</key>
                <string>{142,142}</string>
                <key>textureRect</key>
                <string>{{127,573},{102,116}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>b12.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{6,0}</string>
                <key>spriteSize</key>
                <string>{100,114}</string>
                <key>spriteSourceSize</key>
                <string>{142,142}</string>
                <key>textureRect</key>
                <string>{{143,1},{100,114}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>b13.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{6,0}</string>
                <key>spriteSize</key>
                <string>{98,114}</string>
                <key>spriteSourceSize</key>
                <string>{142,142}</string>
                <key>textureRect</key>
                <string>{{143,117},{98,114}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>b14.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{6,0}</string>
                <key>spriteSize</key>
                <string>{98,114}</string>
                <key>spriteSourceSize</key>
                <string>{142,142}</string>
                <key>textureRect</key>
                <string>{{125,785},{98,114}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>b15.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{6,0}</string>
                <key>spriteSize</key>
                <string>{96,112}</string>
                <key>spriteSourceSize</key>
                <string>{142,142}</string>
                <key>textureRect</key>
                <string>{{109,885},{96,112}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>b16.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{5,0}</string>
                <key>spriteSize</key>
                <string>{98,106}</string>
                <key>spriteSourceSize</key>
                <string>{142,142}</string>
                <key>textureRect</key>
                <string>{{1,885},{98,106}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>b17.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{6,0}</string>
                <key>spriteSize</key>
                <string>{94,110}</string>
                <key>spriteSourceSize</key>
                <string>{142,142}</string>
                <key>textureRect</key>
                <string>{{133,353},{94,110}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>b2.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{3,0}</string>
                <key>spriteSize</key>
                <string>{136,130}</string>
                <key>spriteSourceSize</key>
                <string>{142,142}</string>
                <key>textureRect</key>
                <string>{{1,127},{136,130}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>b3.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{5,0}</string>
                <key>spriteSize</key>
                <string>{124,118}</string>
                <key>spriteSourceSize</key>
                <string>{142,142}</string>
                <key>textureRect</key>
                <string>{{1,509},{124,118}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>b4.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{6,0}</string>
                <key>spriteSize</key>
                <string>{116,122}</string>
                <key>spriteSourceSize</key>
                <string>{142,142}</string>
                <key>textureRect</key>
                <string>{{127,449},{116,122}}</string>
                <key>textureRotated</key>
                <false/>
            </dict>
            <key>b5.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{6,0}</string>
                <key>spriteSize</key>
                <string>{124,140}</string>
                <key>spriteSourceSize</key>
                <string>{142,142}</string>
                <key>textureRect</key>
                <string>{{1,1},{124,140}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>b6.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{6,1}</string>
                <key>spriteSize</key>
                <string>{122,130}</string>
                <key>spriteSourceSize</key>
                <string>{142,142}</string>
                <key>textureRect</key>
                <string>{{1,259},{122,130}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>b7.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{6,0}</string>
                <key>spriteSize</key>
                <string>{112,122}</string>
                <key>spriteSourceSize</key>
                <string>{142,142}</string>
                <key>textureRect</key>
                <string>{{1,629},{112,122}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>b8.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{6,0}</string>
                <key>spriteSize</key>
                <string>{108,122}</string>
                <key>spriteSourceSize</key>
                <string>{142,142}</string>
                <key>textureRect</key>
                <string>{{1,743},{108,122}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
            <key>b9.png</key>
            <dict>
                <key>aliases</key>
                <array/>
                <key>spriteOffset</key>
                <string>{6,0}</string>
                <key>spriteSize</key>
                <string>{106,118}</string>
                <key>spriteSourceSize</key>
                <string>{142,142}</string>
                <key>textureRect</key>
                <string>{{125,677},{106,118}}</string>
                <key>textureRotated</key>
                <true/>
            </dict>
        </dict>
        <key>metadata</key>
        <dict>
            <key>format</key>
            <integer>3</integer>
            <key>pixelFormat</key>
            <string>RGBA8888</string>
            <key>premultiplyAlpha</key>
            <false/>
            <key>realTextureFileName</key>
            <string>b1.png</string>
            <key>size</key>
            <string>{244,984}</string>
            <key>smartupdate</key>
            <string>$TexturePacker:SmartUpdate:9ffb95fa7ae4a0fee65c9330f736bf90:b1d829865ce02c35ca12623d58d020c7:711ea92644afcfa26ff56bf794426a0a$</string>
            <key>textureFileName</key>
            <string>b1.png</string>
        </dict>
    </dict>
</plist>
