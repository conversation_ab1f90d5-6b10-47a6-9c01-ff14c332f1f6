[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "emitter6", "_objFlags": 0, "_parent": null, "_children": [], "_active": true, "_components": [{"__id__": 2}], "_prefab": {"__id__": 3}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "f01d9aOQEtPtI2DlpSDcr8I", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_edit_in_system": true, "_loop": true, "_prefab": {"__uuid__": "e74db7ef-1a71-4883-830c-19aee7e8041b"}, "_delay_min": 0, "_delay_max": 0, "_duration_min": 3, "_duration_max": 3, "_interval_min": 0.1, "_interval_max": 0.1, "_count_min": 1, "_count_max": 1, "origin_angle_min": 0, "origin_angle_max": 0, "switch_reset_angle": false, "_change_angle_min": -35, "_change_angle_max": -35, "off_x_min": 0, "off_x_max": 0, "off_y_min": 0, "off_y_max": 0, "related_owner_position": true, "switch_gravity": false, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": -10}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "665159fd-d188-4d0b-ae61-93f664a19d2e"}, "fileId": "", "sync": false}]