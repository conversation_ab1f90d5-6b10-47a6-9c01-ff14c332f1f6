'use strict';exports['__esModule']=!![];exports['LQCollideSystem']=void 0x0;var lq_const_1=require('../lq_base/data/lq_const');var lq_math_util_1=require('../lq_base/util/lq_math_util');var lq_collide_config_1=require('./lq_collide_config');var lq_data_1=require('../lq_base/data/lq_data');var Vec2=cc['Vec2'];var game=cc['game'];var director=cc['director'];var Scheduler=cc['Scheduler'];var misc=cc['misc'];var LQQuadTree=function(){function _0x2358bd(_0x3c7fa6,_0x54b660,_0x7aab18,_0x585e94){this['collide_arr']=[];this['node_arr']=[];this['max_object']=_0x54b660||0xa;this['max_level']=_0x7aab18||0x4;this['level']=_0x585e94||0x0;this['rect']=_0x3c7fa6;this['collide_arr']=[];this['node_arr']=[];}_0x2358bd['prototype']['split']=function(){var _0x381f3f=this['level']+0x1;var _0x4cb597=this['rect']['width']*0.5;var _0x36c720=this['rect']['height']*0.5;var _0x4b967=this['rect']['x'];var _0x5d0d45=this['rect']['y'];this['node_arr'][0x0]=new _0x2358bd(new lq_data_1['LQRect'](_0x4b967+_0x4cb597,_0x5d0d45,_0x4cb597,_0x36c720),this['max_object'],this['max_level'],_0x381f3f);this['node_arr'][0x1]=new _0x2358bd(new lq_data_1['LQRect'](_0x4b967,_0x5d0d45,_0x4cb597,_0x36c720),this['max_object'],this['max_level'],_0x381f3f);this['node_arr'][0x2]=new _0x2358bd(new lq_data_1['LQRect'](_0x4b967,_0x5d0d45+_0x36c720,_0x4cb597,_0x36c720),this['max_object'],this['max_level'],_0x381f3f);this['node_arr'][0x3]=new _0x2358bd(new lq_data_1['LQRect'](_0x4b967+_0x4cb597,_0x5d0d45+_0x36c720,_0x4cb597,_0x36c720),this['max_object'],this['max_level'],_0x381f3f);};_0x2358bd['prototype']['get_index']=function(_0x56b717){var _0x2fc759=[];var _0x59b0f2=this['rect']['x']+this['rect']['half_width'];var _0x438e88=this['rect']['y']+this['rect']['half_height'];var _0x59c596=_0x56b717['world_rect']['y']+_0x56b717['world_rect']['half_height']>_0x438e88;var _0x292ff9=_0x56b717['world_rect']['x']-_0x56b717['world_rect']['half_width']<_0x59b0f2;var _0x4f397a=_0x56b717['world_rect']['x']+_0x56b717['world_rect']['half_width']>_0x59b0f2;var _0x5a3ffc=_0x56b717['world_rect']['y']-_0x56b717['world_rect']['half_height']<_0x438e88;if(_0x4f397a&&_0x59c596){_0x2fc759['push'](0x0);}if(_0x292ff9&&_0x59c596){_0x2fc759['push'](0x1);}if(_0x292ff9&&_0x5a3ffc){_0x2fc759['push'](0x2);}if(_0x5a3ffc&&_0x4f397a){_0x2fc759['push'](0x3);}return _0x2fc759;};;_0x2358bd['prototype']['insert']=function(_0x3ee5ce){var _0x4faf35;if(this['node_arr']['length']){_0x4faf35=this['get_index'](_0x3ee5ce);for(var _0xf568d=0x0;_0xf568d<_0x4faf35['length'];_0xf568d++){this['node_arr'][_0x4faf35[_0xf568d]]['insert'](_0x3ee5ce);}return;}this['collide_arr']['push'](_0x3ee5ce);if(this['collide_arr']['length']>this['max_object']&&this['level']<this['max_level']){if(!this['node_arr']['length']){this['split']();}for(var _0xf568d=0x0;_0xf568d<this['collide_arr']['length'];_0xf568d++){var _0x1b2ac1=this['collide_arr'][_0xf568d];_0x4faf35=this['get_index'](_0x1b2ac1);for(var _0x26cc5c=0x0;_0x26cc5c<_0x4faf35['length'];_0x26cc5c++){this['node_arr'][_0x4faf35[_0x26cc5c]]['insert'](_0x1b2ac1);}}this['collide_arr']=[];}};;_0x2358bd['prototype']['retrieve']=function(_0x44fae3){var _0x57c92d;var _0x4adf44=this['get_index'](_0x44fae3);if(this['collide_arr']['length']){(_0x57c92d=_0x2358bd['temp_collide_arr'])['push']['apply'](_0x57c92d,this['collide_arr']);}if(this['node_arr']['length']){for(var _0x343da2=0x0;_0x343da2<_0x4adf44['length'];_0x343da2++){this['node_arr'][_0x4adf44[_0x343da2]]['retrieve'](_0x44fae3);}}};;_0x2358bd['prototype']['get_all_area']=function(){if(this['collide_arr']['length']){_0x2358bd['all_collide_arr']['push'](this['collide_arr']);}if(this['node_arr']['length']){for(var _0x20dac5=0x0;_0x20dac5<0x4;_0x20dac5++){this['node_arr'][_0x20dac5]['get_all_area']();}}};_0x2358bd['prototype']['clear']=function(){this['collide_arr']=[];for(var _0x540d26=0x0;_0x540d26<this['node_arr']['length'];_0x540d26++){if(this['node_arr']['length']){this['node_arr'][_0x540d26]['clear']();}}this['node_arr']=[];};;_0x2358bd['temp_collide_arr']=[];_0x2358bd['all_collide_arr']=[];return _0x2358bd;}();var LQCollideSystem=function(){function _0xaf0c72(){}_0xaf0c72['get_axe']=function(_0x228270,_0x52fac5){var _0x2d974f=_0x228270['sub'](_0x52fac5);return new Vec2(_0x2d974f['y'],-_0x2d974f['x'])['normalize']();};_0xaf0c72['get_axes']=function(_0x34bb11,_0x5a9472){if(!_0x34bb11['cache_polygon_points']){this['cache_polygon'](_0x34bb11);this['cache_axes'](_0x34bb11,_0x5a9472);}return _0x34bb11['cache_axes'];};_0xaf0c72['cache_axes']=function(_0x50d892,_0x3ff92b){_0x50d892['cache_axes']=[];if(_0x50d892['_collide_shape']===lq_const_1['LQCollideShape']['Circle']){if(!_0x3ff92b['cache_polygon_points']){this['cache_polygon'](_0x3ff92b);this['cache_axes'](_0x3ff92b,_0x50d892);}var _0xab3ddb=lq_const_1['LQConst']['VEC_ZERO'];var _0x2f01e3=Number['MAX_SAFE_INTEGER'];var _0x2be9c2=_0x3ff92b['cache_polygon_points'];for(var _0x3a4bbc=0x0;_0x3a4bbc<_0x2be9c2['length'];_0x3a4bbc++){var _0x409b42=_0x2be9c2[_0x3a4bbc];var _0x46e890=_0x50d892['world_rect']['sub'](_0x409b42)['magSqr']();if(_0x46e890<_0x2f01e3){_0x2f01e3=_0x46e890;_0xab3ddb=_0x409b42;}}_0x50d892['cache_axes']['push'](this['get_axe'](_0x50d892['world_rect'],_0xab3ddb));}else{var _0x2be9c2=_0x50d892['cache_polygon_points'];for(var _0x3a4bbc=0x0,_0x3de700=_0x2be9c2['length']-0x1;_0x3a4bbc<_0x3de700;_0x3a4bbc++){_0x50d892['cache_axes']['push'](this['get_axe'](_0x2be9c2[_0x3a4bbc],_0x2be9c2[_0x3a4bbc+0x1]));}_0x50d892['cache_axes']['push'](this['get_axe'](_0x2be9c2[0x0],_0x2be9c2[_0x2be9c2['length']-0x1]));}};_0xaf0c72['cache_polygon']=function(_0x4612f3){if(_0x4612f3['_collide_shape']===lq_const_1['LQCollideShape']['Rect']){_0x4612f3['cache_polygon_points']=[];_0x4612f3['cache_polygon_points']['push'](_0x4612f3['world_rect']['bottom_left']());_0x4612f3['cache_polygon_points']['push'](_0x4612f3['world_rect']['bottom_right']());_0x4612f3['cache_polygon_points']['push'](_0x4612f3['world_rect']['top_left']());_0x4612f3['cache_polygon_points']['push'](_0x4612f3['world_rect']['top_right']());}else if(_0x4612f3['_collide_shape']===lq_const_1['LQCollideShape']['Polygon']){_0x4612f3['cache_polygon_points']=[];var _0x522cb2=_0x4612f3['world_rect']['pos']();var _0x2a5249=misc['degreesToRadians'](_0x4612f3['node']['angle']);var _0x20230a=Math['sin'](_0x2a5249);var _0x309df1=Math['cos'](_0x2a5249);var _0x5ce4f9=function(_0x5779ca){return new Vec2(_0x5779ca['x']*_0x309df1-_0x5779ca['y']*_0x20230a,_0x5779ca['x']*_0x20230a+_0x5779ca['y']*_0x309df1);};for(var _0x37bcbe=0x0;_0x37bcbe<_0x4612f3['_polygon_points']['length'];_0x37bcbe++){_0x4612f3['cache_polygon_points']['push'](_0x5ce4f9(_0x4612f3['_polygon_points'][_0x37bcbe])['add'](_0x522cb2));}}};_0xaf0c72['get_projection']=function(_0x4dc918,_0x22877b){if(_0x4dc918['_collide_shape']===lq_const_1['LQCollideShape']['Circle']){var _0xac2ec4=_0x22877b['dot'](_0x4dc918['world_rect']);var _0x82df3f=_0xac2ec4-_0x4dc918['radius'];var _0x53d835=_0xac2ec4+_0x4dc918['radius'];return{'min':Math['min'](_0x82df3f,_0x53d835),'max':Math['max'](_0x82df3f,_0x53d835)};}else{var _0x5053d8=Number['MAX_SAFE_INTEGER'];var _0x587b9c=Number['MIN_SAFE_INTEGER'];for(var _0x4ce7e8=0x0,_0x3ddfc4=_0x4dc918['cache_polygon_points'];_0x4ce7e8<_0x3ddfc4['length'];_0x4ce7e8++){var _0x448593=_0x3ddfc4[_0x4ce7e8];var _0xac2ec4=_0x448593['dot'](_0x22877b);_0x5053d8=Math['min'](_0x5053d8,_0xac2ec4);_0x587b9c=Math['max'](_0x587b9c,_0xac2ec4);}return{'min':_0x5053d8,'max':_0x587b9c};}};_0xaf0c72['intersects_polygon']=function(_0x48b488,_0x589a3f){var _0x3ca046=this['get_axes'](_0x48b488,_0x589a3f);var _0x5ae5dd=this['get_axes'](_0x589a3f,_0x48b488);for(var _0x44ae02=0x0,_0x243356=_0x3ca046;_0x44ae02<_0x243356['length'];_0x44ae02++){var _0x2ee06b=_0x243356[_0x44ae02];var _0x200e87=this['get_projection'](_0x48b488,_0x2ee06b);var _0x5ced6a=this['get_projection'](_0x589a3f,_0x2ee06b);if(_0x200e87['max']<=_0x5ced6a['min']||_0x5ced6a['max']<=_0x200e87['min']){return![];}}for(var _0x552df8=0x0,_0x2a276e=_0x5ae5dd;_0x552df8<_0x2a276e['length'];_0x552df8++){var _0x2ee06b=_0x2a276e[_0x552df8];var _0x200e87=this['get_projection'](_0x48b488,_0x2ee06b);var _0x5ced6a=this['get_projection'](_0x589a3f,_0x2ee06b);if(_0x200e87['max']<=_0x5ced6a['min']||_0x5ced6a['max']<=_0x200e87['min']){return![];}}return!![];};_0xaf0c72['update_world_rect']=function(_0x57ae0e){if(!_0x57ae0e['world_rect']){return;}_0x57ae0e['node']['_updateWorldMatrix']();Vec2['transformMat4'](_0x57ae0e['world_rect'],_0x57ae0e['_offset'],_0x57ae0e['node']['_worldMatrix']);};_0xaf0c72['update_collide_logic']=function(_0x299cab){if(_0x299cab['is_open_func']){for(var _0xd7a184 in _0x299cab['collide_map']){var _0x3f4f96=_0x299cab['collide_map'][_0xd7a184];if(_0x3f4f96['status']===0x1){_0x3f4f96['status']=0x2;}else{delete _0x299cab['collide_map'][_0xd7a184];_0x299cab['on_exit'](_0x3f4f96['collide']);}}}};_0xaf0c72['collide_other']=function(_0x248eb0,_0x5e80bd){if(_0x248eb0['is_open_func']){var _0x54343a=_0x248eb0['collide_map'][_0x5e80bd['collide_id']];if(_0x54343a){_0x54343a['status']=0x1;}else{_0x248eb0['collide_map'][_0x5e80bd['collide_id']]={'collide':_0x5e80bd,'status':0x1};_0x248eb0['on_enter'](_0x5e80bd);}}if(_0x5e80bd['is_open_func']){var _0x16e09f=_0x5e80bd['collide_map'][_0x248eb0['collide_id']];if(_0x16e09f){_0x16e09f['status']=0x1;}else{_0x5e80bd['collide_map'][_0x248eb0['collide_id']]={'collide':_0x248eb0,'status':0x1};_0x5e80bd['on_enter'](_0x248eb0);}}_0x248eb0['on_collide'](_0x5e80bd);_0x5e80bd['on_collide'](_0x248eb0);};_0xaf0c72['add_collide']=function(_0x25e4a5){this['update_world_rect'](_0x25e4a5);this['collide_arr']['push'](_0x25e4a5);};_0xaf0c72['remove_collide']=function(_0x2ba3e9){for(var _0x9109b4=this['collide_arr']['length']-0x1;_0x9109b4>=0x0;_0x9109b4--){if(this['collide_arr'][_0x9109b4]['collide_id']===_0x2ba3e9['collide_id']){this['collide_arr']['splice'](_0x9109b4,0x1);break;}}};_0xaf0c72['update_logic']=function(_0x1abd5c){if(!this['is_enable']){return;}if(lq_collide_config_1['LQCollideConfig']['switch_quad_tree']){this['quad_tree']['clear']();for(var _0x3c38a9=this['collide_arr']['length']-0x1;_0x3c38a9>=0x0;_0x3c38a9--){var _0x4114aa=this['collide_arr'][_0x3c38a9];if(!_0x4114aa){return;}if(!_0x4114aa['isValid']){this['collide_arr']['splice'](_0x3c38a9,0x1);continue;}this['update_collide_logic'](_0x4114aa);if(_0x4114aa['is_enable']){this['update_world_rect'](_0x4114aa);this['quad_tree']['insert'](_0x4114aa);}}LQQuadTree['all_collide_arr']=[];this['quad_tree']['get_all_area']();for(var _0x265c8d=0x0;_0x265c8d<LQQuadTree['all_collide_arr']['length'];_0x265c8d++){var _0x3939a1=LQQuadTree['all_collide_arr'][_0x265c8d];for(var _0x3c38a9=0x0;_0x3c38a9<_0x3939a1['length'];_0x3c38a9++){var _0x1610b1=_0x3939a1[_0x3c38a9];for(var _0x2f955e=_0x3c38a9+0x1;_0x2f955e<_0x3939a1['length'];_0x2f955e++){var _0x3de3f3=_0x3939a1[_0x2f955e];if(_0x1610b1['collide_category']&_0x3de3f3['collide_mask']&&_0x1610b1!==_0x3de3f3){if(_0x1610b1['_collide_shape']===0x1&&_0x3de3f3['_collide_shape']===0x1){if(lq_math_util_1['LQMathUtil']['intersects_rect'](_0x1610b1['world_rect'],_0x3de3f3['world_rect'])){this['collide_other'](_0x1610b1,_0x3de3f3);}}else if(_0x1610b1['_collide_shape']===0x1&&_0x3de3f3['_collide_shape']===0x2){if(lq_math_util_1['LQMathUtil']['intersects_circle_rect'](_0x3de3f3['world_rect'],_0x3de3f3['radius'],_0x1610b1['world_rect'])){this['collide_other'](_0x1610b1,_0x3de3f3);}}else if(_0x1610b1['_collide_shape']===0x2&&_0x3de3f3['_collide_shape']===0x1){if(lq_math_util_1['LQMathUtil']['intersects_circle_rect'](_0x1610b1['world_rect'],_0x1610b1['radius'],_0x3de3f3['world_rect'])){this['collide_other'](_0x1610b1,_0x3de3f3);}}else if(_0x1610b1['_collide_shape']===0x2&&_0x3de3f3['_collide_shape']===0x2){if(lq_math_util_1['LQMathUtil']['intersects_circle'](_0x1610b1['world_rect'],_0x1610b1['radius'],_0x3de3f3['world_rect'],_0x3de3f3['radius'])){this['collide_other'](_0x1610b1,_0x3de3f3);}}else if(this['intersects_polygon'](_0x1610b1,_0x3de3f3)){this['collide_other'](_0x1610b1,_0x3de3f3);}}}}}}else{var _0x3939a1=[];for(var _0x3c38a9=this['collide_arr']['length']-0x1;_0x3c38a9>=0x0;_0x3c38a9--){var _0x4114aa=this['collide_arr'][_0x3c38a9];if(!_0x4114aa){return;}if(!_0x4114aa['isValid']){this['collide_arr']['splice'](_0x3c38a9,0x1);continue;}this['update_collide_logic'](_0x4114aa);if(_0x4114aa['is_enable']){this['update_world_rect'](_0x4114aa);_0x3939a1['push'](_0x4114aa);}}var _0x48220c=_0x3939a1['length'];for(var _0x3c38a9=0x0;_0x3c38a9<_0x48220c;_0x3c38a9++){var _0x1610b1=_0x3939a1[_0x3c38a9];for(var _0x2f955e=_0x3c38a9+0x1;_0x2f955e<_0x48220c;_0x2f955e++){var _0x3de3f3=_0x3939a1[_0x2f955e];if(_0x1610b1['collide_category']&_0x3de3f3['collide_mask']){if(_0x1610b1['_collide_shape']===0x1&&_0x3de3f3['_collide_shape']===0x1){if(lq_math_util_1['LQMathUtil']['intersects_rect'](_0x1610b1['world_rect'],_0x3de3f3['world_rect'])){this['collide_other'](_0x1610b1,_0x3de3f3);}}else if(_0x1610b1['_collide_shape']===0x1&&_0x3de3f3['_collide_shape']===0x2){if(lq_math_util_1['LQMathUtil']['intersects_circle_rect'](_0x3de3f3['world_rect'],_0x3de3f3['radius'],_0x1610b1['world_rect'])){this['collide_other'](_0x1610b1,_0x3de3f3);}}else if(_0x1610b1['_collide_shape']===0x2&&_0x3de3f3['_collide_shape']===0x1){if(lq_math_util_1['LQMathUtil']['intersects_circle_rect'](_0x1610b1['world_rect'],_0x1610b1['radius'],_0x3de3f3['world_rect'])){this['collide_other'](_0x1610b1,_0x3de3f3);}}else if(_0x1610b1['_collide_shape']===0x2&&_0x3de3f3['_collide_shape']===0x2){if(lq_math_util_1['LQMathUtil']['intersects_circle'](_0x1610b1['world_rect'],_0x1610b1['radius'],_0x3de3f3['world_rect'],_0x3de3f3['radius'])){this['collide_other'](_0x1610b1,_0x3de3f3);}}else if(this['intersects_polygon'](_0x1610b1,_0x3de3f3)){this['collide_other'](_0x1610b1,_0x3de3f3);}}}}}};_0xaf0c72['get_group_by_index']=function(_0x3dc002){for(var _0x2d6c99 in lq_collide_config_1['LQCollideConfig']['collide_group_map']){var _0xf7892=lq_collide_config_1['LQCollideConfig']['collide_group_map'][_0x2d6c99];if(_0xf7892['index']===_0x3dc002){return _0xf7892;}}return undefined;};_0xaf0c72['get_info_by_id']=function(_0x2295ba){var _0x4eb749;for(var _0x374cee in lq_collide_config_1['LQCollideConfig']['collide_group_map']){var _0x3717ab=lq_collide_config_1['LQCollideConfig']['collide_group_map'][_0x374cee];if(_0x3717ab['id']===_0x2295ba){return _0x3717ab;}if(!_0x4eb749){_0x4eb749=_0x3717ab;}}return _0x4eb749;};_0xaf0c72['find_nearest_collide']=function(_0x505842){var _0x42de8e=[];for(var _0x58d701=_0xaf0c72['collide_arr']['length']-0x1;_0x58d701>=0x0;_0x58d701--){var _0x3b4812=_0xaf0c72['collide_arr'][_0x58d701];if(_0x505842===_0x3b4812||!_0x3b4812['is_enable']){continue;}if(_0x505842['follow_target_category']){if(_0x3b4812['collide_category']===_0x505842['follow_target_category']){_0x42de8e['push'](_0x3b4812);}}else if((_0x3b4812['collide_category']&_0x505842['collide_mask'])!==0x0){_0x42de8e['push'](_0x3b4812);}}_0x42de8e['sort'](function(_0x307647,_0x395f65){return _0x505842['world_rect']['sub'](_0x307647['world_rect'])['magSqr']()-_0x505842['world_rect']['sub'](_0x395f65['world_rect'])['magSqr']();});return _0x42de8e[0x0];};_0xaf0c72['clear']=function(_0x5a32d5){if(_0x5a32d5===void 0x0){_0x5a32d5=![];}if(_0x5a32d5){for(var _0x3b52d7=this['collide_arr']['length']-0x1;_0x3b52d7>=0x0;_0x3b52d7--){var _0x207f04=this['collide_arr'][_0x3b52d7];if(_0x207f04['isValid']){_0x207f04['node']['destroy']();}}}this['collide_arr']=[];};_0xaf0c72['check_collide']=function(_0x140259){var _0x6e5479=[];var _0x2d9d3a;if(lq_collide_config_1['LQCollideConfig']['switch_quad_tree']){LQQuadTree['temp_collide_arr']=[];this['quad_tree']['retrieve'](_0x140259);_0x2d9d3a=LQQuadTree['temp_collide_arr'];}else{_0x2d9d3a=this['collide_arr'];}for(var _0x29ac79=0x0;_0x29ac79<_0x2d9d3a['length'];_0x29ac79++){var _0x5278d1=_0x2d9d3a[_0x29ac79];if(_0x140259===_0x5278d1){continue;}if(_0x140259['collide_category']&_0x5278d1['collide_mask']){if(_0x140259['_collide_shape']===0x1&&_0x5278d1['_collide_shape']===0x1){if(lq_math_util_1['LQMathUtil']['intersects_rect'](_0x140259['world_rect'],_0x5278d1['world_rect'])){_0x6e5479['push'](_0x5278d1);}}else if(_0x140259['_collide_shape']===0x1&&_0x5278d1['_collide_shape']===0x2){if(lq_math_util_1['LQMathUtil']['intersects_circle_rect'](_0x5278d1['world_rect'],_0x5278d1['radius'],_0x140259['world_rect'])){_0x6e5479['push'](_0x5278d1);}}else if(_0x140259['_collide_shape']===0x2&&_0x5278d1['_collide_shape']===0x1){if(lq_math_util_1['LQMathUtil']['intersects_circle_rect'](_0x140259['world_rect'],_0x140259['radius'],_0x5278d1['world_rect'])){_0x6e5479['push'](_0x5278d1);}}else if(_0x140259['_collide_shape']===0x2&&_0x5278d1['_collide_shape']===0x2){if(lq_math_util_1['LQMathUtil']['intersects_circle'](_0x140259['world_rect'],_0x140259['radius'],_0x5278d1['world_rect'],_0x5278d1['radius'])){_0x6e5479['push'](_0x5278d1);}}else if(this['intersects_polygon'](_0x140259,_0x5278d1)){_0x6e5479['push'](_0x5278d1);}}}return _0x6e5479;};_0xaf0c72['is_enable']=![];_0xaf0c72['collide_arr']=[];_0xaf0c72['quad_tree']=new LQQuadTree(new lq_data_1['LQRect'](lq_collide_config_1['LQCollideConfig']['active_area_x'],lq_collide_config_1['LQCollideConfig']['active_area_y'],lq_collide_config_1['LQCollideConfig']['active_area_width'],lq_collide_config_1['LQCollideConfig']['active_area_height']),lq_collide_config_1['LQCollideConfig']['max_node_len'],lq_collide_config_1['LQCollideConfig']['max_node_level']);return _0xaf0c72;}();exports['LQCollideSystem']=LQCollideSystem;var AutoRun=function(){function _0x58b431(){}_0x58b431['prototype']['update']=function(_0x39a9ae){LQCollideSystem['update_logic'](_0x39a9ae);};return _0x58b431;}();game['on'](game['EVENT_GAME_INITED'],function(){if(lq_collide_config_1['LQCollideConfig']['switch_auto_run']&&!CC_EDITOR){var _0x273211=new AutoRun();director['getScheduler']()['enableForTarget'](_0x273211);director['getScheduler']()['scheduleUpdate'](_0x273211,Scheduler['PRIORITY_SYSTEM'],![]);}});