var resPkg = {
    "Sounds": {
        assetType: cc.AudioClip,
        urls: [
            "enemy_blowup",
            "gun_fire",
            "bossComing",
            "gameOver",
            "victory",
            "dun",
            "hedan",
            "emitter2",
            "fire3",
            "bgm",
            "pop",
            "cuantianhou",
            "daodan1",
            "addHP",
            "enemy_b1",
            "enemy_b2"

        ],
    },

    "ui_prefabs": {
        assetType: cc.Prefab,
        urls: [

            "GameUI",
            "Over_UI",
            "Victory_UI",





        ],
    },


    "Gui": {
        assetType: cc.Prefab,
        urls: [
            //发射器  不要忘了添加
            "emitter/emitter1",
            "emitter/emitter2",
            "emitter/emitter3",
            "emitter/emitter4",
            "emitter/emitter5",
            "emitter/emitter6",
            "emitter/emitter10",
            "emitter/player1_emitter",
            "emitter/player2_emitter",
            "emitter/player3_emitter",
            "emitter/player4_emitter",
            "emitter/player5_emitter",



            "player/play_shield",


            //玩家  以后改成动态加载
            "player/player1",
            "player/player2",
            "player/player3",
            "player/player4",
            "player/player5",




            //宠物  以后改成动态加载 
            "pet/pet_1",
            "pet/pet_2",
            "pet/pet_3",
            "pet/pet_4",



            "other/itemLevel",
            "other/hedanBomb"





        ],
    },







    "Maps": {
        assetType: cc.SpriteFrame,
        urls: [
            //  "gameBg3"
        ],
    },


    // "Charactors": {
    //     assetType: cc.Prefab,
    //     urls: [

    //     ],
    // },
};

export default resPkg;
