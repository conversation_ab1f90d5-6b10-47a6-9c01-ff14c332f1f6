[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "player5_emitter", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 5}, {"__id__": 8}, {"__id__": 11}], "_active": true, "_components": [{"__id__": 14}], "_prefab": {"__id__": 15}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -40, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "b1", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}], "_prefab": {"__id__": 4}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-55, 51, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "f01d9aOQEtPtI2DlpSDcr8I", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_edit_in_system": true, "_loop": true, "_prefab": {"__uuid__": "61a601b9-7bd9-4455-8ba1-de9c31cba827"}, "_delay_min": 0, "_delay_max": 0, "_duration_min": 3, "_duration_max": 3, "_interval_min": 0.5, "_interval_max": 0.5, "_count_min": 1, "_count_max": 1, "origin_angle_min": 0, "origin_angle_max": 0, "switch_reset_angle": false, "_change_angle_min": 0, "_change_angle_max": 0, "off_x_min": 0, "off_x_max": 0, "off_y_min": 0, "off_y_max": 0, "related_owner_position": true, "switch_gravity": false, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": -10}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "bc3be508-73f4-4c27-99b9-3d311c7eb9c6"}, "fileId": "c4bI52dHhH3b92y2OpQjxu", "sync": false}, {"__type__": "cc.Node", "_name": "b2", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 6}], "_prefab": {"__id__": 7}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [55, 46, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "f01d9aOQEtPtI2DlpSDcr8I", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "_edit_in_system": true, "_loop": true, "_prefab": {"__uuid__": "61a601b9-7bd9-4455-8ba1-de9c31cba827"}, "_delay_min": 0, "_delay_max": 0, "_duration_min": 3, "_duration_max": 3, "_interval_min": 0.5, "_interval_max": 0.5, "_count_min": 1, "_count_max": 1, "origin_angle_min": 0, "origin_angle_max": 0, "switch_reset_angle": false, "_change_angle_min": 0, "_change_angle_max": 0, "off_x_min": 0, "off_x_max": 0, "off_y_min": 0, "off_y_max": 0, "related_owner_position": true, "switch_gravity": false, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": -10}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "bc3be508-73f4-4c27-99b9-3d311c7eb9c6"}, "fileId": "e2CHSjXIhD3ZFJEEIyU+7A", "sync": false}, {"__type__": "cc.Node", "_name": "s1", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 9}], "_prefab": {"__id__": 10}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [30, 46, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "f01d9aOQEtPtI2DlpSDcr8I", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "_edit_in_system": true, "_loop": true, "_prefab": {"__uuid__": "3abd8548-4959-42ea-b8d0-192d2b023bc9"}, "_delay_min": 0, "_delay_max": 0, "_duration_min": 3, "_duration_max": 3, "_interval_min": 0.1, "_interval_max": 0.1, "_count_min": 1, "_count_max": 1, "origin_angle_min": 90, "origin_angle_max": 90, "switch_reset_angle": false, "_change_angle_min": 0, "_change_angle_max": 0, "off_x_min": 0, "off_x_max": 0, "off_y_min": 0, "off_y_max": 0, "related_owner_position": true, "switch_gravity": false, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": -10}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "bc3be508-73f4-4c27-99b9-3d311c7eb9c6"}, "fileId": "700xqdpORB/bGLzgQivEok", "sync": false}, {"__type__": "cc.Node", "_name": "s1 copy", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 12}], "_prefab": {"__id__": 13}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 28, "height": 44}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-30, 46, 0, 0, 0, 0, 1, 1.5, 1.5, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "f01d9aOQEtPtI2DlpSDcr8I", "_name": "", "_objFlags": 0, "node": {"__id__": 11}, "_enabled": true, "_edit_in_system": true, "_loop": true, "_prefab": {"__uuid__": "3abd8548-4959-42ea-b8d0-192d2b023bc9"}, "_delay_min": 0, "_delay_max": 0, "_duration_min": 3, "_duration_max": 3, "_interval_min": 0.1, "_interval_max": 0.1, "_count_min": 1, "_count_max": 1, "origin_angle_min": 90, "origin_angle_max": 90, "switch_reset_angle": false, "_change_angle_min": 0, "_change_angle_max": 0, "off_x_min": 0, "off_x_max": 0, "off_y_min": 0, "off_y_max": 0, "related_owner_position": true, "switch_gravity": false, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": -10}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "bc3be508-73f4-4c27-99b9-3d311c7eb9c6"}, "fileId": "26Ybxk8XpOLZ39yv5CpBWw", "sync": false}, {"__type__": "f01d9aOQEtPtI2DlpSDcr8I", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_edit_in_system": true, "_loop": true, "_prefab": {"__uuid__": "efbf88cf-2901-455b-8e02-a0fb083ff361"}, "_delay_min": 0, "_delay_max": 0, "_duration_min": 3, "_duration_max": 3, "_interval_min": 0.1, "_interval_max": 0.1, "_count_min": 1, "_count_max": 1, "origin_angle_min": 0, "origin_angle_max": 0, "switch_reset_angle": false, "_change_angle_min": 0, "_change_angle_max": 0, "off_x_min": 0, "off_x_max": 0, "off_y_min": 0, "off_y_max": 0, "related_owner_position": true, "switch_gravity": false, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": -10}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "bc3be508-73f4-4c27-99b9-3d311c7eb9c6"}, "fileId": "", "sync": false}]