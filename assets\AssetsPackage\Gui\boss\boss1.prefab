[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "boss1", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 5}, {"__id__": 8}, {"__id__": 12}, {"__id__": 18}, {"__id__": 25}], "_active": true, "_components": [{"__id__": 32}, {"__id__": 33}, {"__id__": 34}, {"__id__": 35}, {"__id__": 36}], "_prefab": {"__id__": 37}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 308, "height": 233}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 2, "groupIndex": 2, "_id": ""}, {"__type__": "cc.Node", "_name": "emitter", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}], "_prefab": {"__id__": 4}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -14.161, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "f01d9aOQEtPtI2DlpSDcr8I", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_edit_in_system": true, "_loop": true, "_prefab": {"__uuid__": "0cbde45a-16ed-4bd3-ae1b-42af0a3aa415"}, "_delay_min": 0, "_delay_max": 0, "_duration_min": 3, "_duration_max": 3, "_interval_min": 2, "_interval_max": 2, "_count_min": 5, "_count_max": 5, "origin_angle_min": -60, "origin_angle_max": -60, "switch_reset_angle": true, "_change_angle_min": 30, "_change_angle_max": 30, "off_x_min": 0, "off_x_max": 0, "off_y_min": 0, "off_y_max": 0, "related_owner_position": true, "switch_gravity": false, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": -10}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "51bf6d2a-455e-48c4-8f21-cf79c9e2ac4e"}, "fileId": "674wYpsHJClJKldwy5AbVv", "sync": false}, {"__type__": "cc.Node", "_name": "plane", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 6}], "_prefab": {"__id__": 7}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 182, "height": 231}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 10.271, 0, 0, 0, 0, 1, 1.8, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 5}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "56b3c67c-1ab0-4018-8d67-bde054db8b65"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "51bf6d2a-455e-48c4-8f21-cf79c9e2ac4e"}, "fileId": "47ic+T5SZClYEYqQQfGGkA", "sync": false}, {"__type__": "cc.Node", "_name": "New Sprite", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 9}, {"__id__": 10}], "_prefab": {"__id__": 11}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 158.316, 0, 0, 0, 1, 6.123233995736766e-17, 1, 1, 1.188]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 180}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "cfb63092-1496-4ef0-946e-1515ca01ec79"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 8}, "_enabled": true, "_defaultClip": {"__uuid__": "6947e4fd-4fc6-4824-aa48-1faec5dd48d8"}, "_clips": [{"__uuid__": "6947e4fd-4fc6-4824-aa48-1faec5dd48d8"}], "playOnLoad": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "51bf6d2a-455e-48c4-8f21-cf79c9e2ac4e"}, "fileId": "e9LbkNYwxAtY2l0nSof8va", "sync": false}, {"__type__": "cc.Node", "_name": "hp", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 13}], "_active": true, "_components": [{"__id__": 16}], "_prefab": {"__id__": 17}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 15}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-100, 207, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "hpR", "_objFlags": 0, "_parent": {"__id__": 12}, "_children": [], "_active": true, "_components": [{"__id__": 14}], "_prefab": {"__id__": 15}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 0, "b": 0, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 200, "height": 15}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 13}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "51bf6d2a-455e-48c4-8f21-cf79c9e2ac4e"}, "fileId": "c8rYe9iaVHCbnwqTQW1PTh", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 12}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "a23235d1-15db-4b95-8439-a2e005bfff91"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "51bf6d2a-455e-48c4-8f21-cf79c9e2ac4e"}, "fileId": "dbXEOWXBtIFIB0byHCjQGc", "sync": false}, {"__type__": "cc.Node", "_name": "pao1", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 19}], "_active": true, "_components": [{"__id__": 22}, {"__id__": 23}], "_prefab": {"__id__": 24}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 99}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5018668885458641, "y": 0.3015320066985376}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-150, 43.469, 0, 0, 0, 0, 1, 1, 1, 1.949]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "w1", "_objFlags": 0, "_parent": {"__id__": 18}, "_children": [], "_active": true, "_components": [{"__id__": 20}], "_prefab": {"__id__": 21}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [1e-05, 53, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "f01d9aOQEtPtI2DlpSDcr8I", "_name": "", "_objFlags": 0, "node": {"__id__": 19}, "_enabled": true, "_edit_in_system": true, "_loop": true, "_prefab": {"__uuid__": "1222a650-d2ee-4717-ae7e-83adc71ce22a"}, "_delay_min": 0, "_delay_max": 0, "_duration_min": 3, "_duration_max": 3, "_interval_min": 0.3, "_interval_max": 0.3, "_count_min": 3, "_count_max": 3, "origin_angle_min": 60, "origin_angle_max": 60, "switch_reset_angle": true, "_change_angle_min": 30, "_change_angle_max": 30, "off_x_min": 0, "off_x_max": 0, "off_y_min": 0, "off_y_max": 0, "related_owner_position": true, "switch_gravity": false, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": -10}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "51bf6d2a-455e-48c4-8f21-cf79c9e2ac4e"}, "fileId": "f5oRxBUBtLq6wNUjuLacfY", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f85c01a4-a20a-41a8-b2d5-21df54f4085b"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cf674DI0fNIapRaxbdN/plm", "_name": "", "_objFlags": 0, "node": {"__id__": 18}, "_enabled": true, "left_arms": {"__id__": 20}, "left_arms_times_over": 180, "left_arms_fire_times_over": 60, "arms_degree": 30, "isFire": false, "isFire_y": false, "kaiPao": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "51bf6d2a-455e-48c4-8f21-cf79c9e2ac4e"}, "fileId": "e2MQPDoWFIIpO6y6mQ6MkO", "sync": false}, {"__type__": "cc.Node", "_name": "pao2", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 26}], "_active": true, "_components": [{"__id__": 29}, {"__id__": 30}], "_prefab": {"__id__": 31}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 64, "height": 99}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5018668885458641, "y": 0.3015320066985376}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [150, 43.469, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "w2", "_objFlags": 0, "_parent": {"__id__": 25}, "_children": [], "_active": true, "_components": [{"__id__": 27}], "_prefab": {"__id__": 28}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 53, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "f01d9aOQEtPtI2DlpSDcr8I", "_name": "", "_objFlags": 0, "node": {"__id__": 26}, "_enabled": true, "_edit_in_system": true, "_loop": true, "_prefab": {"__uuid__": "1222a650-d2ee-4717-ae7e-83adc71ce22a"}, "_delay_min": 0, "_delay_max": 0, "_duration_min": 3, "_duration_max": 3, "_interval_min": 0.3, "_interval_max": 0.3, "_count_min": 1, "_count_max": 1, "origin_angle_min": 90, "origin_angle_max": 90, "switch_reset_angle": true, "_change_angle_min": 0, "_change_angle_max": 0, "off_x_min": 0, "off_x_max": 0, "off_y_min": 0, "off_y_max": 0, "related_owner_position": true, "switch_gravity": false, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": -10}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "51bf6d2a-455e-48c4-8f21-cf79c9e2ac4e"}, "fileId": "e3CrLFm0hBpJKYfUTOJmJP", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 25}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f85c01a4-a20a-41a8-b2d5-21df54f4085b"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cf674DI0fNIapRaxbdN/plm", "_name": "", "_objFlags": 0, "node": {"__id__": 25}, "_enabled": true, "left_arms": {"__id__": 27}, "left_arms_times_over": 180, "left_arms_fire_times_over": 60, "arms_degree": 0, "isFire": false, "isFire_y": false, "kaiPao": null, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "51bf6d2a-455e-48c4-8f21-cf79c9e2ac4e"}, "fileId": "836KPjwdROTaWt3JL0GQ5Q", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": null, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "64832dFcFFAYJpRo7KNtRC9", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "speed": 100, "score": 800, "bullet_emitter_arr": {"__id__": 3}, "hp": 850, "fire_num_hp": 90, "arms1": {"__id__": 18}, "arms2": {"__id__": 25}, "_id": ""}, {"__type__": "fff9bqLmFFGOY0yWNU1rBNI", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "spriteFrames": [{"__uuid__": "d8bbfab3-322a-4ebc-b910-4a8495a8401b"}, {"__uuid__": "e7000d5a-f042-4719-8925-fe7c47a1a257"}, {"__uuid__": "3b95f01a-e18d-46b7-a248-408202a13fb3"}, {"__uuid__": "e37c436e-f3b5-4f42-9c98-08b80ac9f45e"}, {"__uuid__": "59d479d3-afe8-4e57-9f62-ab830da9016c"}, {"__uuid__": "bf0b5092-be05-44f3-9eb6-5379a69b59da"}, {"__uuid__": "0673ebaa-9f69-442e-83a7-68f174b01dba"}, {"__uuid__": "c7f60a43-1b41-4514-adc8-fc1be55a7fa2"}, {"__uuid__": "8cc2b809-3082-4c0e-b31c-0fe9c55c5356"}, {"__uuid__": "bd6e67d0-6c81-420c-987a-1feb69b1da6c"}, {"__uuid__": "5d16b6e7-6706-4778-beb6-e24a44e0c915"}, {"__uuid__": "daca1551-39ff-4311-bbc5-e1e542e211b5"}, {"__uuid__": "8022a0b8-24d0-48ac-99c7-45b49fba3a01"}, {"__uuid__": "eeca8aad-7e5b-495f-979b-3836ffe74bcc"}, {"__uuid__": "95c927e2-81c4-4675-abff-d7b8eba201c4"}, {"__uuid__": "dbc6acba-d2e3-4330-b8d4-1e28bd4a37ac"}], "duration": 0.05, "node_scale": 4, "loop": false, "playOnload": false, "_id": ""}, {"__type__": "ecae3yDq7VA2KSyjFe8ZRcm", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_id": ""}, {"__type__": "eebe12+b/JL64foUWRsM+wp", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_draw_collide": false, "_collide_shape": 1, "collide_group_id": 4, "_radius": 100, "_size": {"__type__": "cc.Size", "width": 300, "height": 50}, "_polygon_points": [{"__type__": "cc.Vec2", "x": 100, "y": 30}, {"__type__": "cc.Vec2", "x": 100, "y": -20}, {"__type__": "cc.Vec2", "x": 100, "y": 0}, {"__type__": "cc.Vec2", "x": 0, "y": 0}, {"__type__": "cc.Vec2", "x": 0, "y": 0}, {"__type__": "cc.Vec2", "x": 0, "y": 0}, {"__type__": "cc.Vec2", "x": 0, "y": 0}, {"__type__": "cc.Vec2", "x": 0, "y": 0}, {"__type__": "cc.Vec2", "x": 0, "y": 0}, {"__type__": "cc.Vec2", "x": 0, "y": 0}], "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 40}, "data_string": "", "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "51bf6d2a-455e-48c4-8f21-cf79c9e2ac4e"}, "fileId": "", "sync": false}]