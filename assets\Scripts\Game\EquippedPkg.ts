var resPkg = {
    "Sounds": {
        assetType: cc.AudioClip,
        urls: [
           
        ],
    },

    "ui_prefabs": {
        assetType: cc.Prefab,
        urls: [
            "Equipped_UI"
        ],
    },


    "Gui": {
        assetType: cc.Prefab,
        urls: [
            //滚动条里的预制体
            "other/playerItem",
            "other/equippedItem",
            "other/petItem",


            //技能图标预制体
            "other/skill_1",
            "other/skill_2",

            "other/shield",
            "other/power",
            "other/hedan",




            //玩家得有5个
            "player/player1",
            "player/player2",
            "player/player3",
            "player/player4",
            "player/player5",


            //宠物
            "pet/pet_1",
            "pet/pet_2",
            "pet/pet_3",
            "pet/pet_4",
      

        ],
    },

}
export default resPkg;//电子邮件************************
//源码网站 开vpn全局模式打开 http://web3incubators.com/
//电报https://t.me/gamecode999
