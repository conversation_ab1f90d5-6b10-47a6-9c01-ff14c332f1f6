/* FishGenData filed desic: (13 fileds)
	id: string
	fishName: string
	roadIndex: int
	isRevert: int
	genTime: int
	isRandom: int
	scale: int
	min_speed: int
	max_speed: int
	hp: int
	coinValue: int
	expValue: int
	use_AI: int
*/

var filed_data = {
	key_1: ["1", "cheqiyu", 0, 0, 0, 0, 600, 5000, 15000, 300, 500, 100, 1, ], 
	key_2: ["2", "dinianyu", 1, 1, 0, 0, 700, 5000, 15000, 300, 500, 100, 1, ], 
	key_3: ["3", "fangyu", 2, 0, 0, 0, 800, 5000, 15000, 300, 500, 100, 1, ], 
	key_4: ["4", "haigui", 3, 1, 0, 0, 900, 5000, 15000, 300, 500, 100, 1, ], 
	key_5: ["5", "hetun", 4, 0, 0, 0, 600, 5000, 15000, 300, 500, 100, 1, ], 
	key_6: ["6", "jin<PERSON>angyu", 5, 1, 0, 0, 700, 5000, 15000, 300, 500, 100, 1, ], 
	key_7: ["7", "shiziyu", 6, 0, 0, 0, 800, 5000, 15000, 300, 500, 100, 1, ], 
	key_8: ["8", "tianshiyu", 7, 1, 0, 0, 900, 5000, 15000, 300, 500, 100, 1, ], 
	key_9: ["9", "xiaochouyu", 8, 0, 0, 0, 600, 5000, 15000, 300, 500, 100, 1, ], 
	key_10: ["10", "xiaohuangyu", 9, 1, 0, 0, 700, 5000, 15000, 300, 500, 100, 1, ], 
	key_11: ["11", "cheqiyu", 10, 0, 0, 0, 800, 5000, 15000, 300, 500, 100, 1, ], 
	key_12: ["12", "dinianyu", 11, 1, 0, 0, 900, 5000, 15000, 300, 500, 100, 1, ], 
	key_13: ["13", "fangyu", 12, 0, 0, 0, 600, 5000, 15000, 300, 500, 100, 1, ], 
	key_14: ["14", "haigui", 13, 1, 0, 0, 700, 5000, 15000, 300, 500, 100, 1, ], 
	key_15: ["15", "hetun", 14, 0, 0, 0, 800, 5000, 15000, 300, 500, 100, 1, ], 
	key_16: ["16", "jinqiangyu", 15, 1, 0, 0, 900, 5000, 15000, 300, 500, 100, 1, ], 
	key_17: ["17", "shiziyu", 16, 0, 0, 0, 600, 5000, 15000, 300, 500, 100, 1, ], 
	key_18: ["18", "tianshiyu", 17, 1, 0, 0, 700, 5000, 15000, 300, 500, 100, 1, ], 
	key_19: ["19", "xiaochouyu", 18, 0, 0, 0, 800, 5000, 15000, 300, 500, 100, 1, ], 
	key_20: ["20", "xiaohuangyu", 19, 1, 0, 0, 900, 5000, 15000, 300, 500, 100, 1, ], 
	key_21: ["21", "cheqiyu", 20, 0, 0, 0, 600, 5000, 15000, 300, 500, 100, 1, ], 
	key_22: ["22", "dinianyu", 21, 1, 0, 0, 700, 5000, 15000, 300, 500, 100, 1, ], 
	key_23: ["23", "fangyu", 22, 0, 0, 0, 800, 5000, 15000, 300, 500, 100, 1, ], 
	key_24: ["24", "haigui", 23, 1, 0, 0, 900, 5000, 15000, 300, 500, 100, 1, ], 
	key_25: ["25", "hetun", 24, 0, 0, 0, 600, 5000, 15000, 300, 500, 100, 1, ], 
	key_26: ["26", "jinqiangyu", 25, 1, 0, 0, 700, 5000, 15000, 300, 500, 100, 1, ], 
	key_27: ["27", "shiziyu", 26, 0, 0, 0, 800, 5000, 15000, 300, 500, 100, 1, ], 
	key_28: ["28", "tianshiyu", 27, 1, 0, 1, 900, 5000, 15000, 300, 500, 100, 1, ], 
	key_29: ["29", "xiaochouyu", 28, 0, 0, 1, 600, 5000, 15000, 300, 500, 100, 1, ], 
	key_30: ["30", "xiaohuangyu", 29, 1, 0, 1, 700, 5000, 15000, 300, 500, 100, 1, ], 
	key_31: ["31", "cheqiyu", 30, 0, 0, 1, 800, 5000, 15000, 300, 500, 100, 1, ], 
	key_32: ["32", "dinianyu", 31, 1, 0, 1, 900, 5000, 15000, 300, 500, 100, 1, ], 
	key_33: ["33", "fangyu", 32, 0, 0, 1, 600, 5000, 15000, 300, 500, 100, 1, ], 
	key_34: ["34", "haigui", 33, 1, 0, 1, 700, 5000, 15000, 300, 500, 100, 1, ], 
	key_35: ["35", "hetun", 34, 0, 0, 1, 800, 5000, 15000, 300, 500, 100, 1, ], 
	key_36: ["36", "jinqiangyu", 35, 1, 0, 1, 900, 5000, 15000, 300, 500, 100, 1, ], 
	key_37: ["37", "shiziyu", 36, 0, 0, 1, 600, 5000, 15000, 300, 500, 100, 1, ], 
	key_38: ["38", "tianshiyu", 37, 1, 0, 1, 700, 5000, 15000, 300, 500, 100, 1, ], 
	key_39: ["39", "xiaochouyu", 38, 0, 0, 1, 800, 5000, 15000, 300, 500, 100, 1, ], 
	key_40: ["40", "xiaohuangyu", 39, 1, 0, 1, 900, 5000, 15000, 300, 500, 100, 1, ], 
	key_41: ["41", "shayu", 40, 1, 0, 1, 800, 5000, 5000, 3000, 5000, 1000, 1, ], 
	key_42: ["42", "shayu2", 41, 1, 50000, 1, 1000, 5000, 5000, 3000, 5000, 1000, 1, ], 
	total_count: 42
};

function get_record(id) {
	var key = "key_" + id;
	var record_array = filed_data[key];
	if(!record_array) {
		 return null;
	}

	var record = {
		id: record_array[0],
		fishName: record_array[1],
		roadIndex: record_array[2],
		isRevert: record_array[3],
		genTime: record_array[4],
		isRandom: record_array[5],
		scale: record_array[6],
		min_speed: record_array[7],
		max_speed: record_array[8],
		hp: record_array[9],
		coinValue: record_array[10],
		expValue: record_array[11],
		use_AI: record_array[12],
	}; 

	return record; 
}

var FishGenData = { 
	filed_data_array: filed_data, 
	get_record: get_record, 
}; 

 export { FishGenData };