[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "pet_1", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 6}, {"__id__": 9}], "_active": true, "_components": [{"__id__": 15}, {"__id__": 16}, {"__id__": 17}], "_prefab": {"__id__": 18}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 199, "height": 130}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 0.6, 0.6, 0.191]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "New Sprite", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 4}], "_prefab": {"__id__": 5}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 60, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-1.976, -71.722, 0, 0, 0, 0, 1, 0.7, 0.7, 0.782]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "0a5ff8f4-9e32-436d-8869-a8e717e080c0"}, "_type": 0, "_sizeMode": 0, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_defaultClip": {"__uuid__": "6947e4fd-4fc6-4824-aa48-1faec5dd48d8"}, "_clips": [{"__uuid__": "6947e4fd-4fc6-4824-aa48-1faec5dd48d8"}], "playOnLoad": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "db367f08-462e-42d5-ba6a-32cf6354b815"}, "fileId": "64GfrTfx1KDIl4ly5wp4uU", "sync": false}, {"__type__": "cc.Node", "_name": "emitter", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 7}], "_prefab": {"__id__": 8}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "f01d9aOQEtPtI2DlpSDcr8I", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "_edit_in_system": true, "_loop": true, "_prefab": {"__uuid__": "ebec5aa7-2d33-4ced-a9f2-53f3e9460891"}, "_delay_min": 0, "_delay_max": 0, "_duration_min": 3, "_duration_max": 3, "_interval_min": 0.5, "_interval_max": 0.5, "_count_min": 1, "_count_max": 1, "origin_angle_min": 0, "origin_angle_max": 0, "switch_reset_angle": true, "_change_angle_min": 0, "_change_angle_max": 0, "off_x_min": 0, "off_x_max": 0, "off_y_min": 0, "off_y_max": 0, "related_owner_position": true, "switch_gravity": false, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": -10}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "db367f08-462e-42d5-ba6a-32cf6354b815"}, "fileId": "17fuEHYsdE4Jl9/5X/H4iv", "sync": false}, {"__type__": "cc.Node", "_name": "pao1", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [{"__id__": 10}], "_active": true, "_components": [{"__id__": 13}], "_prefab": {"__id__": 14}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 115}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Node", "_name": "w1", "_objFlags": 0, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 11}], "_prefab": {"__id__": 12}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "f01d9aOQEtPtI2DlpSDcr8I", "_name": "", "_objFlags": 0, "node": {"__id__": 10}, "_enabled": true, "_edit_in_system": true, "_loop": true, "_prefab": {"__uuid__": "5d2ee8c0-9f10-4df2-827c-51d2be040618"}, "_delay_min": 0, "_delay_max": 0, "_duration_min": 3, "_duration_max": 3, "_interval_min": 2, "_interval_max": 2, "_count_min": 1, "_count_max": 1, "origin_angle_min": 0, "origin_angle_max": 0, "switch_reset_angle": true, "_change_angle_min": 0, "_change_angle_max": 0, "off_x_min": 0, "off_x_max": 0, "off_y_min": 0, "off_y_max": 0, "related_owner_position": true, "switch_gravity": false, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": -10}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "db367f08-462e-42d5-ba6a-32cf6354b815"}, "fileId": "30CRgtIExLkYgIPCQ5kzQp", "sync": false}, {"__type__": "8f36fQmnvxLGJM3h6Ji+eWV", "_name": "", "_objFlags": 0, "node": {"__id__": 9}, "_enabled": true, "left_arms": {"__id__": 11}, "arms_degree": 0, "tagatIsPlayer": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "db367f08-462e-42d5-ba6a-32cf6354b815"}, "fileId": "a465nURrBEqKyxTPaD94c8", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "7d4b30c2-e5c6-4d00-a252-c068ccef571a"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "74be0Ty/lFMJoSuKOLEELGo", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "speed": 900, "_id": ""}, {"__type__": "f246dSp7eNBlqvpIllyUkmr", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "think_time": 0.03, "len": 150, "isTow": true, "fire_RorL": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "db367f08-462e-42d5-ba6a-32cf6354b815"}, "fileId": "", "sync": false}]