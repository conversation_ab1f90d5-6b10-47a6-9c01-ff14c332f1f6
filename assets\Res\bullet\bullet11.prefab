[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "bullet1", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}], "_active": true, "_components": [{"__id__": 5}, {"__id__": 6}, {"__id__": 7}, {"__id__": 8}, {"__id__": 9}], "_prefab": {"__id__": 10}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 76, "height": 37}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 3, "groupIndex": 3, "_id": ""}, {"__type__": "cc.Node", "_name": "New Node", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}], "_prefab": {"__id__": 4}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [-32, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.MotionStreak", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_fadeTime": 0.1, "_minSeg": 1, "_stroke": 15, "_texture": {"__uuid__": "14f956d9-544d-488d-ac5d-5a36ce854312"}, "_color": {"__type__": "cc.Color", "r": 255, "g": 214, "b": 0, "a": 255}, "_fastMode": false, "_N$preview": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cb316414-4a5d-4867-8cba-be22dde5134f"}, "fileId": "358Y5mYpZMIaaF+F98UFj1", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "b80ca526-c5fb-4356-beba-6ecfe5d2a24b"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "ecae3yDq7VA2KSyjFe8ZRcm", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_id": ""}, {"__type__": "696cfyjGn9D/ZWvdoUClUj+", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_draw_collide": false, "_collide_shape": 2, "collide_group_id": 3, "_radius": 20, "_size": {"__type__": "cc.Size", "width": 100, "height": 100}, "_polygon_points": [{"__type__": "cc.Vec2", "x": -100, "y": -50}, {"__type__": "cc.Vec2", "x": 0, "y": 100}, {"__type__": "cc.Vec2", "x": 100, "y": -50}], "_offset": {"__type__": "cc.Vec2", "x": 10, "y": 0}, "data_string": "", "life": 3, "speed": 30, "switch_speed_curve": false, "_speed_timeline": [{"__type__": "cc.Vec2", "x": 0, "y": 0}, {"__type__": "cc.Vec2", "x": 1, "y": 1}], "angle": 0, "related_self_angle": true, "is_custom_pos": false, "flip_x": false, "flip_y": false, "switch_follow_target": false, "follow_target_mode": 0, "follow_target_range": 80, "_id": ""}, {"__type__": "3026enysp9CsrGDb8zSzDMY", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "bulletPower": 2, "_id": ""}, {"__type__": "fff9bqLmFFGOY0yWNU1rBNI", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "spriteFrames": [{"__uuid__": "64319856-ffb7-4c14-9343-571ae01b9aec"}, {"__uuid__": "bc9e64e2-42eb-4754-b0d2-21bde56400cd"}, {"__uuid__": "abe916a5-9e16-49e8-8fd4-16b3d5f2b12a"}, {"__uuid__": "a1b1bef1-1455-4cc0-b2fb-9c7c89ea0710"}, {"__uuid__": "e7138228-7132-4b12-9642-85f1cad3f156"}], "duration": 0.02, "node_scale": 1.5, "loop": false, "playOnload": false, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "cb316414-4a5d-4867-8cba-be22dde5134f"}, "fileId": "", "sync": false}]