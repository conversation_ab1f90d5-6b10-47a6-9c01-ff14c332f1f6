[{"__type__": "cc.Prefab", "_name": "", "_objFlags": 0, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "asyncLoadAssets": false, "readonly": false}, {"__type__": "cc.Node", "_name": "enemy10", "_objFlags": 0, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 6}], "_active": true, "_components": [{"__id__": 9}, {"__id__": 10}, {"__id__": 11}, {"__id__": 12}, {"__id__": 13}, {"__id__": 14}, {"__id__": 15}], "_prefab": {"__id__": 16}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 130, "height": 185}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, 0, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 2, "groupIndex": 2, "_id": ""}, {"__type__": "cc.Node", "_name": "New Sprite", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}, {"__id__": 4}], "_prefab": {"__id__": 5}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 186, "height": 172}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5039623655913978, "y": 0.42286046511627906}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0.6, -1.3, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "f5749bef-bbdc-40f8-99fa-c67634ae30fe"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "cc.Animation", "_name": "", "_objFlags": 0, "node": {"__id__": 2}, "_enabled": true, "_defaultClip": {"__uuid__": "3c7eb25a-3b48-4bb5-a391-6fab2c4497b7"}, "_clips": [{"__uuid__": "3c7eb25a-3b48-4bb5-a391-6fab2c4497b7"}], "playOnLoad": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b9f0116c-e1ee-413b-ab35-d02436a948fa"}, "fileId": "b6yppO3T5KCL6kg3fgclVl", "sync": false}, {"__type__": "cc.Node", "_name": "fire", "_objFlags": 0, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 7}], "_prefab": {"__id__": 8}, "_opacity": 255, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_contentSize": {"__type__": "cc.Size", "width": 0, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_trs": {"__type__": "TypedArray", "ctor": "Float64Array", "array": [0, -119, 0, 0, 0, 0, 1, 1, 1, 1]}, "_eulerAngles": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_skewX": 0, "_skewY": 0, "_is3DNode": false, "_groupIndex": 0, "groupIndex": 0, "_id": ""}, {"__type__": "f01d9aOQEtPtI2DlpSDcr8I", "_name": "", "_objFlags": 0, "node": {"__id__": 6}, "_enabled": true, "_edit_in_system": true, "_loop": true, "_prefab": {"__uuid__": "09918b74-aa67-4128-8adf-55e6a1adfbbd"}, "_delay_min": 0, "_delay_max": 0, "_duration_min": 3, "_duration_max": 3, "_interval_min": 3, "_interval_max": 3, "_count_min": 1, "_count_max": 1, "origin_angle_min": -90, "origin_angle_max": -90, "switch_reset_angle": true, "_change_angle_min": 0, "_change_angle_max": 0, "off_x_min": 0, "off_x_max": 0, "off_y_min": 0, "off_y_max": 0, "related_owner_position": true, "switch_gravity": false, "gravity": {"__type__": "cc.Vec2", "x": 0, "y": -10}, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b9f0116c-e1ee-413b-ab35-d02436a948fa"}, "fileId": "3e8yDUWHRCD6HeW9aiRhYW", "sync": false}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_materials": [{"__uuid__": "eca5d2f2-8ef6-41c2-bbe6-f9c79d09c432"}], "_srcBlendFactor": 770, "_dstBlendFactor": 771, "_spriteFrame": {"__uuid__": "9c512caf-2a91-4cc1-a38a-8e05f659c956"}, "_type": 0, "_sizeMode": 1, "_fillType": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_atlas": null, "_id": ""}, {"__type__": "fff9bqLmFFGOY0yWNU1rBNI", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "spriteFrames": [{"__uuid__": "455d3357-1043-45e5-a2c6-d414b762d611"}, {"__uuid__": "8d56d0e1-adee-426e-8cc6-18f95df86587"}, {"__uuid__": "a14f9e56-b267-472f-ab68-f1ecf463d3cc"}, {"__uuid__": "4b1f131b-6aa2-4837-bb97-d28ca0d12f9a"}, {"__uuid__": "51a199e5-7446-414b-b656-14fd387daf10"}, {"__uuid__": "41a1433a-fa57-45dd-804e-23cee369994e"}, {"__uuid__": "063b15d4-b915-4f41-8dda-797a4c7ee840"}], "duration": 0.1, "node_scale": 3, "loop": false, "playOnload": false, "_id": ""}, {"__type__": "ecae3yDq7VA2KSyjFe8ZRcm", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_id": ""}, {"__type__": "eebe12+b/JL64foUWRsM+wp", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "_draw_collide": false, "_collide_shape": 1, "collide_group_id": 4, "_radius": 50, "_size": {"__type__": "cc.Size", "width": 50, "height": 100}, "_polygon_points": [{"__type__": "cc.Vec2", "x": -100, "y": -50}, {"__type__": "cc.Vec2", "x": 0, "y": 100}, {"__type__": "cc.Vec2", "x": 100, "y": -50}], "_offset": {"__type__": "cc.Vec2", "x": 0, "y": -30}, "data_string": "", "_id": ""}, {"__type__": "b7093Zi7odPh4rhzTnbqBvD", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "fire_wq": [{"__id__": 7}], "speed": 500, "_id": ""}, {"__type__": "59eee2uGBVOPpiIsCt3Rnzq", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "think_time": 0.03, "attack_R": 600, "_id": ""}, {"__type__": "c4a5646vqlIvIgSAi9P+s87", "_name": "", "_objFlags": 0, "node": {"__id__": 1}, "_enabled": true, "speed": 150, "score": 3, "bullet_emitter": {"__id__": 7}, "hp": 3, "isShip": false, "amount": 2, "isOnlyONe": false, "type": 2, "pos_enemy": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "stop_por": 0, "isReward": true, "isAttack_speed": true, "isHp_v": true, "sound": "enemy_blowup", "drawBack": true, "_id": ""}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__uuid__": "b9f0116c-e1ee-413b-ab35-d02436a948fa"}, "fileId": "", "sync": false}]